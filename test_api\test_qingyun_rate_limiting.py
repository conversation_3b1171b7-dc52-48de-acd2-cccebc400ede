#!/usr/bin/env python3
"""
测试QingYun API速率限制功能
验证回测系统是否正确为QingYun API模型添加30秒延迟
"""

import sys
import os
import time
from datetime import datetime
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.append(project_root)

from src.backtester import Backtester


def mock_agent(**kwargs):
    """模拟交易代理，返回空的决策和信号"""
    return {
        "decisions": {},
        "analyst_signals": {}
    }


def test_qingyun_rate_limiting():
    """测试QingYun API模型的速率限制功能"""
    print("🧪 测试QingYun API速率限制功能")
    print("=" * 50)

    # 测试QingYun模型列表
    qingyun_models = [
        "claude-3-5-sonnet-latest",
        "claude-3-7-sonnet-latest",
        "gemini-2.0-flash",
        "gemini-2.5-pro-exp-03-25",
        "gpt-4.5-preview",
        "gpt-4o",
        "gpt-3.5-turbo",
        "o3",
        "o4-mini",
        "meta-llama/llama-4-scout",
        "meta-llama/llama-4-maverick",
        "grok-beta"
    ]

    for model_name in qingyun_models:
        print(f"\n📋 测试模型: {model_name}")

        # 创建回测器实例
        backtester = Backtester(
            agent=mock_agent,
            tickers=["AAPL"],
            start_date="2024-01-01",
            end_date="2024-01-02",
            initial_capital=100000,
            model_name=model_name,
            model_provider="QingYun"
        )
        
        # 测试_should_apply_rate_limit方法
        should_limit = backtester._should_apply_rate_limit()
        print(f"   ✅ _should_apply_rate_limit(): {should_limit}")
        assert should_limit == True, f"QingYun模型 {model_name} 应该应用速率限制"
        
        # 测试_apply_rate_limit_delay方法（模拟time.sleep）
        with patch('time.sleep') as mock_sleep:
            start_time = time.time()
            backtester._apply_rate_limit_delay("2024-01-01")
            end_time = time.time()
            
            # 验证time.sleep被调用了一次，参数为30秒
            mock_sleep.assert_called_once_with(30)
            print(f"   ✅ 延迟时间: 30秒")
            print(f"   ✅ 实际执行时间: {end_time - start_time:.3f}秒 (模拟)")
    
    print(f"\n🎉 所有QingYun模型速率限制测试通过!")


def test_non_qingyun_models():
    """测试非QingYun模型不应用速率限制"""
    print("\n🧪 测试非QingYun模型不应用速率限制")
    print("=" * 50)
    
    # 测试其他提供商的模型
    test_cases = [
        ("deepseek-chat", "DeepSeek"),
        ("gemini-2.0-flash", "Gemini"),
        ("meta-llama/llama-4-scout-17b-16e-instruct", "Groq"),
        ("gpt-4o", "OpenAI"),
        ("claude-3-5-sonnet-latest", "Anthropic")
    ]
    
    for model_name, provider in test_cases:
        if provider == "Groq" or provider == "OpenRouter":
            # 这些提供商有自己的速率限制逻辑，跳过测试
            continue
            
        print(f"\n📋 测试模型: {model_name} ({provider})")
        
        backtester = Backtester(
            agent=mock_agent,
            tickers=["AAPL"],
            start_date="2024-01-01",
            end_date="2024-01-02",
            initial_capital=100000,
            model_name=model_name,
            model_provider=provider
        )
        
        # 测试_should_apply_rate_limit方法
        should_limit = backtester._should_apply_rate_limit()
        print(f"   ✅ _should_apply_rate_limit(): {should_limit}")
        assert should_limit == False, f"非QingYun模型 {model_name} ({provider}) 不应该应用速率限制"
        
        # 测试_apply_rate_limit_delay方法
        with patch('time.sleep') as mock_sleep:
            start_time = time.time()
            backtester._apply_rate_limit_delay("2024-01-01")
            end_time = time.time()
            
            # 验证time.sleep没有被调用
            mock_sleep.assert_not_called()
            print(f"   ✅ 无延迟 (正确)")
    
    print(f"\n🎉 非QingYun模型速率限制测试通过!")


def test_rate_limiting_integration():
    """集成测试：验证速率限制在实际回测流程中的工作"""
    print("\n🧪 集成测试：速率限制在回测流程中的应用")
    print("=" * 50)
    
    # 使用QingYun模型创建回测器
    backtester = Backtester(
        agent=mock_agent,
        tickers=["AAPL"],
        start_date="2024-01-01",
        end_date="2024-01-02",
        initial_capital=100000,
        model_name="gpt-4o",
        model_provider="QingYun"
    )
    
    print(f"📋 测试配置:")
    print(f"   模型: {backtester.model_name}")
    print(f"   提供商: {backtester.model_provider}")
    print(f"   应用速率限制: {backtester._should_apply_rate_limit()}")
    
    # 模拟在回测循环中调用速率限制
    with patch('time.sleep') as mock_sleep:
        print(f"\n⏱️  模拟交易日结束后的速率限制...")
        backtester._apply_rate_limit_delay("2024-01-01")
        
        # 验证延迟被正确应用
        mock_sleep.assert_called_once_with(30)
        print(f"   ✅ 成功应用30秒延迟")
    
    print(f"\n🎉 集成测试通过!")


if __name__ == "__main__":
    print("🚀 开始QingYun API速率限制测试")
    print("=" * 60)
    
    try:
        # 运行所有测试
        test_qingyun_rate_limiting()
        test_non_qingyun_models()
        test_rate_limiting_integration()
        
        print("\n" + "=" * 60)
        print("🎊 所有测试通过! QingYun API速率限制功能正常工作")
        print("📝 功能说明:")
        print("   • QingYun API模型在每个交易日结束后等待30秒")
        print("   • 其他提供商的模型不受影响")
        print("   • 速率限制有助于防止QingYun API服务器饱和")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        sys.exit(1)
