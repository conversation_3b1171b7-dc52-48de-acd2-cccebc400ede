#!/usr/bin/env python3
"""
QingYun API速率限制功能演示
展示回测系统如何为QingYun API模型添加30秒延迟
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.append(project_root)

from src.backtester import Backtester


def mock_agent(**kwargs):
    """模拟交易代理，返回空的决策和信号"""
    return {
        "decisions": {},
        "analyst_signals": {}
    }


def demo_qingyun_rate_limiting():
    """演示QingYun API速率限制功能"""
    print("🚀 QingYun API速率限制功能演示")
    print("=" * 60)
    
    # 创建使用QingYun API的回测器
    print("📋 创建回测器配置:")
    print("   • 模型: gpt-4o")
    print("   • 提供商: QingYun")
    print("   • 股票: AAPL")
    print("   • 时间范围: 2024-01-01 到 2024-01-03 (3个交易日)")
    print("   • 初始资金: $100,000")
    
    backtester = Backtester(
        agent=mock_agent,
        tickers=["AAPL"],
        start_date="2024-01-01",
        end_date="2024-01-03",
        initial_capital=100000,
        model_name="gpt-4o",
        model_provider="QingYun"
    )
    
    print(f"\n✅ 回测器创建成功")
    print(f"   • 速率限制检查: {backtester._should_apply_rate_limit()}")
    
    # 模拟多个交易日的速率限制
    print(f"\n⏱️  模拟多个交易日的速率限制演示:")
    print("   (实际回测中，每个交易日结束后会自动应用延迟)")
    
    trading_days = ["2024-01-01", "2024-01-02", "2024-01-03"]
    
    for i, day in enumerate(trading_days, 1):
        print(f"\n📅 交易日 {i}: {day}")
        print("   • 执行交易决策...")
        print("   • 更新投资组合...")
        print("   • 计算绩效指标...")
        
        # 应用速率限制延迟
        if i < len(trading_days):  # 最后一天不需要延迟
            print("   • 应用速率限制延迟...")
            start_time = time.time()
            backtester._apply_rate_limit_delay(day)
            end_time = time.time()
            print(f"   ✅ 延迟完成 (实际等待: {end_time - start_time:.1f}秒)")
        else:
            print("   ✅ 最后一个交易日，无需延迟")
    
    print(f"\n🎉 演示完成!")
    print(f"📊 总结:")
    print(f"   • 处理了 {len(trading_days)} 个交易日")
    print(f"   • 应用了 {len(trading_days) - 1} 次速率限制延迟")
    print(f"   • 每次延迟: 30秒")
    print(f"   • 总延迟时间: {(len(trading_days) - 1) * 30}秒")


def compare_providers():
    """比较不同提供商的速率限制行为"""
    print(f"\n🔍 不同提供商速率限制比较")
    print("=" * 60)
    
    providers_config = [
        ("QingYun", "gpt-4o", True, "30秒"),
        ("OpenAI", "gpt-4o", False, "无延迟"),
        ("Anthropic", "claude-3-5-sonnet-latest", False, "无延迟"),
        ("Groq", "meta-llama/llama-4-scout-17b-16e-instruct", True, "60秒"),
        ("OpenRouter", "meta-llama/llama-4-scout:free", True, "180秒"),
    ]
    
    print("📋 速率限制配置对比:")
    print("   提供商        模型                                    速率限制    延迟时间")
    print("   " + "-" * 80)
    
    for provider, model, has_limit, delay in providers_config:
        backtester = Backtester(
            agent=mock_agent,
            tickers=["AAPL"],
            start_date="2024-01-01",
            end_date="2024-01-02",
            initial_capital=100000,
            model_name=model,
            model_provider=provider
        )
        
        actual_limit = backtester._should_apply_rate_limit()
        status = "✅" if actual_limit == has_limit else "❌"
        
        print(f"   {provider:<12} {model:<35} {str(actual_limit):<10} {delay:<10} {status}")
    
    print(f"\n💡 说明:")
    print(f"   • QingYun API: 30秒延迟，防止服务器饱和")
    print(f"   • Groq Llama: 60秒延迟，30次/分钟限制")
    print(f"   • OpenRouter免费: 180秒延迟，20次/分钟限制")
    print(f"   • 其他提供商: 无延迟，使用各自的速率限制策略")


def usage_recommendations():
    """使用建议"""
    print(f"\n📚 QingYun API速率限制使用建议")
    print("=" * 60)
    
    print("🎯 适用场景:")
    print("   • 长期回测 (多天/多周/多月)")
    print("   • 多股票组合回测")
    print("   • 需要稳定API访问的场景")
    
    print(f"\n⚙️  配置建议:")
    print("   • 使用QingYun API时会自动启用30秒延迟")
    print("   • 无需手动配置，系统自动检测")
    print("   • 支持所有QingYun API模型")
    
    print(f"\n🚀 性能影响:")
    print("   • 单日回测: 无影响")
    print("   • 10日回测: 增加4.5分钟 (9 × 30秒)")
    print("   • 30日回测: 增加14.5分钟 (29 × 30秒)")
    print("   • 252日回测: 增加2.1小时 (251 × 30秒)")
    
    print(f"\n💰 成本效益:")
    print("   • 防止API限制错误")
    print("   • 提高回测成功率")
    print("   • 减少重试和失败成本")
    print("   • 保护QingYun API服务稳定性")


if __name__ == "__main__":
    try:
        demo_qingyun_rate_limiting()
        compare_providers()
        usage_recommendations()
        
        print(f"\n" + "=" * 60)
        print("🎊 QingYun API速率限制功能已成功集成到回测系统!")
        print("🔧 使用方法: 选择任何QingYun提供商的模型即可自动启用")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        sys.exit(1)
