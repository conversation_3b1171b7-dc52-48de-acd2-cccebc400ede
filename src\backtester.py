import sys
import os
import time

# 将项目根目录添加到 Python 路径
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import questionary
import json

import matplotlib.pyplot as plt
import pandas as pd
from colorama import Fore, Style, init
import numpy as np
import itertools

from src.llm.models import LLM_ORDER, OLLAMA_LLM_ORDER, get_model_info, ModelProvider
from src.utils.analysts import ANALYST_ORDER
from src.main import run_hedge_fund
from src.tools.api import (
    get_company_news,
    get_price_data,
    get_prices,
    get_financial_metrics,
    get_insider_trades,
)
from src.analysis.realtime_accuracy_tracker import RealtimeAccuracyTracker
from src.utils.display import print_backtest_results, format_backtest_row
from typing_extensions import Callable
from src.utils.ollama import ensure_ollama_and_model

# Import deterministic utilities
try:
    from src.utils.deterministic import (
        set_deterministic_mode,
        is_deterministic_mode,
        get_experiment_id,
        create_deterministic_filename,
        log_deterministic_state,
        DeterministicContext
    )
    DETERMINISTIC_AVAILABLE = True
except ImportError:
    DETERMINISTIC_AVAILABLE = False
    print("Warning: Deterministic utilities not available. System may exhibit non-deterministic behavior.")

init(autoreset=True)


class Backtester:
    def __init__(
        self,
        agent: Callable,
        tickers: list[str],
        start_date: str,
        end_date: str,
        initial_capital: float,
        model_name: str = "gpt-4o",
        model_provider: str = "OpenAI",
        selected_analysts: list[str] = [],
        initial_margin_requirement: float = 0.0,
        show_reasoning: bool = False,
        save_reasoning: bool = False,
        save_input_data: bool = False,
        track_accuracy: bool = False,
    ):
        """
        :param agent: The trading agent (Callable).
        :param tickers: List of tickers to backtest.
        :param start_date: Start date string (YYYY-MM-DD).
        :param end_date: End date string (YYYY-MM-DD).
        :param initial_capital: Starting portfolio cash.
        :param model_name: Which LLM model name to use (gpt-4, etc).
        :param model_provider: Which LLM provider (OpenAI, etc).
        :param selected_analysts: List of analyst names or IDs to incorporate.
        :param initial_margin_requirement: The margin ratio (e.g. 0.5 = 50%).
        :param show_reasoning: Whether to display reasoning in terminal.
        :param save_reasoning: Whether to save reasoning to files.
        :param save_input_data: Whether to save input data to files.
        :param track_accuracy: Whether to track signal accuracy in real-time.
        """
        self.agent = agent
        self.tickers = tickers
        self.start_date = start_date
        self.end_date = end_date
        self.initial_capital = initial_capital
        self.model_name = model_name
        self.model_provider = model_provider
        self.selected_analysts = selected_analysts
        self.show_reasoning = show_reasoning
        self.save_reasoning = save_reasoning
        self.save_input_data = save_input_data
        self.track_accuracy = track_accuracy

        # Create reasoning logs directory if save_reasoning or save_input_data is enabled
        if self.save_reasoning or self.save_input_data:
            self.reasoning_dir = self._create_reasoning_directory()

        # Initialize accuracy tracker if enabled
        if self.track_accuracy:
            self.accuracy_tracker = RealtimeAccuracyTracker(
                reasoning_logs_dir="reasoning_logs" if not hasattr(self, 'reasoning_dir') else os.path.dirname(self.reasoning_dir)
            )
            print("📊 实时准确性跟踪已启用")

        # Initialize input data manager if enabled
        if self.save_input_data:
            from src.utils.input_data_manager import InputDataManager
            self.input_data_manager = InputDataManager(self.reasoning_dir)
            print("💾 输入数据保存已启用")

        # Initialize portfolio with support for long/short positions
        self.portfolio_values = []
        self.portfolio = {
            "cash": self.initial_capital,
            "margin_used": 0.5,  # total margin usage across all short positions
            "margin_requirement": initial_margin_requirement,  # The margin ratio required for shorts
            "positions": {ticker: {"long": 0, "short": 0, "long_cost_basis": 0.0, "short_cost_basis": 0.0, "short_margin_used": 0.0} for ticker in self.tickers},  # Number of shares held long  # Number of shares held short  # Average cost basis per share (long)  # Average cost basis per share (short)  # Dollars of margin used for this ticker's short
            "realized_gains": {
                ticker: {
                    "long": 0.0,  # Realized gains from long positions
                    "short": 0.0,  # Realized gains from short positions
                }
                for ticker in self.tickers
            },
        }

    def _should_apply_rate_limit(self) -> bool:
        """
        Check if rate limiting should be applied for the current model.
        Returns True if the model needs rate limiting.
        """
        # GROQ Llama model rate limiting
        if (self.model_name == "meta-llama/llama-4-scout-17b-16e-instruct" and
            self.model_provider == "Groq"):
            return True

        # OpenRouter free models rate limiting
        if (self.model_provider == "OpenRouter" and
            ":free" in self.model_name):
            return True

        # QingYun API models rate limiting
        if self.model_provider == "QingYun":
            return True

        return False

    def _apply_rate_limit_delay(self, current_date_str: str):
        """
        Apply rate limiting delay for models that need it.
        Adds appropriate delays between trading days to avoid hitting API rate limits.
        """
        if not self._should_apply_rate_limit():
            return

        # GROQ Llama model (30 calls/minute limit)
        if (self.model_name == "meta-llama/llama-4-scout-17b-16e-instruct" and
            self.model_provider == "Groq"):
            print(f"{Fore.YELLOW}Rate limit protection: Waiting 60 seconds before next trading day "
                  f"(meta-llama/llama-4-scout-17b-16e-instruct has 30 calls/minute limit)...{Style.RESET_ALL}")
            time.sleep(60)
            print(f"{Fore.GREEN}Rate limit delay completed. Continuing with next trading day.{Style.RESET_ALL}")

        # OpenRouter free models (20 calls/minute limit)
        elif (self.model_provider == "OpenRouter" and ":free" in self.model_name):
            print(f"{Fore.YELLOW}Rate limit protection: Waiting 180 seconds before next trading day "
                  f"({self.model_name} has 20 calls/minute limit, using conservative delay)...{Style.RESET_ALL}")
            time.sleep(180)  # 3 minutes delay for extra safety
            print(f"{Fore.GREEN}Rate limit delay completed. Continuing with next trading day.{Style.RESET_ALL}")

        # QingYun API models (rate limiting to prevent server saturation)
        elif self.model_provider == "QingYun":
            print(f"{Fore.YELLOW}Rate limit protection: Waiting 30 seconds before next trading day "
                  f"(QingYun API {self.model_name} - preventing server saturation)...{Style.RESET_ALL}")
            time.sleep(30)
            print(f"{Fore.GREEN}Rate limit delay completed. Continuing with next trading day.{Style.RESET_ALL}")

    def execute_trade(self, ticker: str, action: str, quantity: float, current_price: float):
        """
        Execute trades with support for both long and short positions.
        `quantity` is the number of shares the agent wants to buy/sell/short/cover.
        We will only trade integer shares to keep it simple.
        """
        if quantity <= 0:
            return 0

        quantity = int(quantity)  # force integer shares
        position = self.portfolio["positions"][ticker]

        if action == "buy":
            cost = quantity * current_price
            if cost <= self.portfolio["cash"]:
                # Weighted average cost basis for the new total
                old_shares = position["long"]
                old_cost_basis = position["long_cost_basis"]
                new_shares = quantity
                total_shares = old_shares + new_shares

                if total_shares > 0:
                    total_old_cost = old_cost_basis * old_shares
                    total_new_cost = cost
                    position["long_cost_basis"] = (total_old_cost + total_new_cost) / total_shares

                position["long"] += quantity
                self.portfolio["cash"] -= cost
                return quantity
            else:
                # Calculate maximum affordable quantity
                max_quantity = int(self.portfolio["cash"] / current_price)
                if max_quantity > 0:
                    cost = max_quantity * current_price
                    old_shares = position["long"]
                    old_cost_basis = position["long_cost_basis"]
                    total_shares = old_shares + max_quantity

                    if total_shares > 0:
                        total_old_cost = old_cost_basis * old_shares
                        total_new_cost = cost
                        position["long_cost_basis"] = (total_old_cost + total_new_cost) / total_shares

                    position["long"] += max_quantity
                    self.portfolio["cash"] -= cost
                    return max_quantity
                return 0

        elif action == "sell":
            # You can only sell as many as you own
            quantity = min(quantity, position["long"])
            if quantity > 0:
                # Realized gain/loss using average cost basis
                avg_cost_per_share = position["long_cost_basis"] if position["long"] > 0 else 0
                realized_gain = (current_price - avg_cost_per_share) * quantity
                self.portfolio["realized_gains"][ticker]["long"] += realized_gain

                position["long"] -= quantity
                self.portfolio["cash"] += quantity * current_price

                if position["long"] == 0:
                    position["long_cost_basis"] = 0.0

                return quantity

        elif action == "short":
            """
            Typical short sale flow:
              1) Receive proceeds = current_price * quantity
              2) Post margin_required = proceeds * margin_ratio
              3) Net effect on cash = +proceeds - margin_required
            """
            proceeds = current_price * quantity
            margin_required = proceeds * self.portfolio["margin_requirement"]
            if margin_required <= self.portfolio["cash"]:
                # Weighted average short cost basis
                old_short_shares = position["short"]
                old_cost_basis = position["short_cost_basis"]
                new_shares = quantity
                total_shares = old_short_shares + new_shares

                if total_shares > 0:
                    total_old_cost = old_cost_basis * old_short_shares
                    total_new_cost = current_price * new_shares
                    position["short_cost_basis"] = (total_old_cost + total_new_cost) / total_shares

                position["short"] += quantity

                # Update margin usage
                position["short_margin_used"] += margin_required
                self.portfolio["margin_used"] += margin_required

                # Increase cash by proceeds, then subtract the required margin
                self.portfolio["cash"] += proceeds
                self.portfolio["cash"] -= margin_required
                return quantity
            else:
                # Calculate maximum shortable quantity
                margin_ratio = self.portfolio["margin_requirement"]
                if margin_ratio > 0:
                    max_quantity = int(self.portfolio["cash"] / (current_price * margin_ratio))
                else:
                    max_quantity = 0

                if max_quantity > 0:
                    proceeds = current_price * max_quantity
                    margin_required = proceeds * margin_ratio

                    old_short_shares = position["short"]
                    old_cost_basis = position["short_cost_basis"]
                    total_shares = old_short_shares + max_quantity

                    if total_shares > 0:
                        total_old_cost = old_cost_basis * old_short_shares
                        total_new_cost = current_price * max_quantity
                        position["short_cost_basis"] = (total_old_cost + total_new_cost) / total_shares

                    position["short"] += max_quantity
                    position["short_margin_used"] += margin_required
                    self.portfolio["margin_used"] += margin_required

                    self.portfolio["cash"] += proceeds
                    self.portfolio["cash"] -= margin_required
                    return max_quantity
                return 0

        elif action == "cover":
            """
            When covering shares:
              1) Pay cover cost = current_price * quantity
              2) Release a proportional share of the margin
              3) Net effect on cash = -cover_cost + released_margin
            """
            quantity = min(quantity, position["short"])
            if quantity > 0:
                cover_cost = quantity * current_price
                avg_short_price = position["short_cost_basis"] if position["short"] > 0 else 0
                realized_gain = (avg_short_price - current_price) * quantity

                if position["short"] > 0:
                    portion = quantity / position["short"]
                else:
                    portion = 1.0

                margin_to_release = portion * position["short_margin_used"]

                position["short"] -= quantity
                position["short_margin_used"] -= margin_to_release
                self.portfolio["margin_used"] -= margin_to_release

                # Pay the cost to cover, but get back the released margin
                self.portfolio["cash"] += margin_to_release
                self.portfolio["cash"] -= cover_cost

                self.portfolio["realized_gains"][ticker]["short"] += realized_gain

                if position["short"] == 0:
                    position["short_cost_basis"] = 0.0
                    position["short_margin_used"] = 0.0

                return quantity

        return 0

    def _create_reasoning_directory(self):
        """Create base directory structure for reasoning logs organized by experiment date."""
        # Use current date as experiment date, but support deterministic mode
        if DETERMINISTIC_AVAILABLE and is_deterministic_mode():
            # In deterministic mode, use a fixed experiment date
            experiment_date = "2025-01-01"
            experiment_id = get_experiment_id() or "deterministic"
            experiment_dir_name = f"{experiment_id}_{self.tickers[0] if self.tickers else 'unknown'}"
        else:
            # Use current date as experiment date
            experiment_date = datetime.now().strftime("%Y-%m-%d")
            experiment_dir_name = f"experiment_{experiment_date}"

        # Create base reasoning directory
        base_dir = os.path.join(project_root, "reasoning_logs")
        experiment_dir = os.path.join(base_dir, experiment_dir_name)

        # Create base experiment directory if it doesn't exist
        os.makedirs(experiment_dir, exist_ok=True)

        print(f"推理日志将保存到: {experiment_dir}")
        return experiment_dir

    def _create_trading_date_directory(self, trading_date):
        """Create subdirectory for specific trading date within the experiment directory."""
        trading_date_dir = os.path.join(self.reasoning_dir, trading_date)
        os.makedirs(trading_date_dir, exist_ok=True)
        return trading_date_dir

    def _save_agent_reasoning(self, output, agent_name, ticker, current_date):
        """Save agent reasoning to file with structured naming."""
        if not self.save_reasoning:
            return

        # Create subdirectory for the specific trading date
        trading_date_dir = self._create_trading_date_directory(current_date)

        # Create filename with ticker, agent name, and timestamp
        if DETERMINISTIC_AVAILABLE and is_deterministic_mode():
            # In deterministic mode, use a fixed timestamp
            filename = create_deterministic_filename(f"{current_date}_{ticker}_{agent_name}", ".json")
        else:
            # Use actual timestamp with microseconds for uniqueness
            timestamp = datetime.now().strftime("%H%M%S%f")[:-3]  # Include milliseconds
            filename = f"{current_date}_{ticker}_{agent_name}_{timestamp}.json"

        filepath = os.path.join(trading_date_dir, filename)

        # Convert output to serializable format (similar to show_agent_reasoning)
        def convert_to_serializable(obj):
            if hasattr(obj, "to_dict"):  # Handle Pandas Series/DataFrame
                return obj.to_dict()
            elif hasattr(obj, "__dict__"):  # Handle custom objects
                return obj.__dict__
            elif isinstance(obj, (int, float, bool, str)):
                return obj
            elif isinstance(obj, (list, tuple)):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: convert_to_serializable(value) for key, value in obj.items()}
            else:
                return str(obj)  # Fallback to string representation

        # Prepare data to save
        reasoning_data = {
            "experiment_date": current_date,
            "ticker": ticker,
            "agent_name": agent_name,
            "timestamp": datetime.now().isoformat(),
            "reasoning": convert_to_serializable(output)
        }

        # Save to JSON file
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(reasoning_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存推理日志时出错: {e}")

    def calculate_portfolio_value(self, current_prices):
        """
        Calculate total portfolio value, including:
          - cash
          - market value of long positions
          - unrealized gains/losses for short positions
        """
        total_value = self.portfolio["cash"]

        for ticker in self.tickers:
            position = self.portfolio["positions"][ticker]
            price = current_prices[ticker]

            # Long position value
            long_value = position["long"] * price
            total_value += long_value

            # Short position unrealized PnL = short_shares * (short_cost_basis - current_price)
            if position["short"] > 0:
                total_value -= position["short"] * price

        return total_value

    def prefetch_data(self):
        """Pre-fetch all data needed for the backtest period."""
        print("\nPre-fetching data for the entire backtest period...")

        # Convert end_date string to datetime, fetch up to 1 year before
        end_date_dt = datetime.strptime(self.end_date, "%Y-%m-%d")
        start_date_dt = end_date_dt - relativedelta(years=1)
        start_date_str = start_date_dt.strftime("%Y-%m-%d")

        for ticker in self.tickers:
            # Fetch price data for the entire period, plus 1 year
            get_prices(ticker, start_date_str, self.end_date)

            # Fetch financial metrics
            get_financial_metrics(ticker, self.end_date, limit=10)

            # Fetch insider trades
            get_insider_trades(ticker, self.end_date, start_date=self.start_date, limit=1000)

            # Fetch company news
            get_company_news(ticker, self.end_date, start_date=self.start_date, limit=1000)

        print("Data pre-fetch complete.")

    def run_backtest(self):
        # Pre-fetch all data at the start
        self.prefetch_data()

        dates = pd.date_range(self.start_date, self.end_date, freq="B")
        table_rows = []
        performance_metrics = {"sharpe_ratio": None, "sortino_ratio": None, "max_drawdown": None, "long_short_ratio": None, "gross_exposure": None, "net_exposure": None}

        print("\nStarting backtest...")

        # Initialize portfolio values list with initial capital
        if len(dates) > 0:
            self.portfolio_values = [{"Date": dates[0], "Portfolio Value": self.initial_capital}]
        else:
            self.portfolio_values = []

        for i, current_date in enumerate(dates):
            lookback_start = (current_date - timedelta(days=30)).strftime("%Y-%m-%d")
            current_date_str = current_date.strftime("%Y-%m-%d")
            previous_date_str = (current_date - timedelta(days=1)).strftime("%Y-%m-%d")

            # 在交易日开始时，不立即清理缓存
            # 让所有代理共享同一天的SPY数据

            # Skip if there's no prior day to look back (i.e., first date in the range)
            if lookback_start == current_date_str:
                continue

            # Get current prices for all tickers
            try:
                current_prices = {}
                missing_data = False

                for ticker in self.tickers:
                    try:
                        price_data = get_price_data(ticker, previous_date_str, current_date_str)
                        if price_data.empty:
                            print(f"Warning: No price data for {ticker} on {current_date_str}")
                            missing_data = True
                            break
                        current_prices[ticker] = price_data.iloc[-1]["close"]
                    except Exception as e:
                        print(f"Error fetching price for {ticker} between {previous_date_str} and {current_date_str}: {e}")
                        missing_data = True
                        break

                if missing_data:
                    print(f"Skipping trading day {current_date_str} due to missing price data")
                    continue

            except Exception as e:
                # If there's a general API error, log it and skip this day
                print(f"Error fetching prices for {current_date_str}: {e}")
                continue

            # ---------------------------------------------------------------
            # 1) Set up input data collection for this trading date
            # ---------------------------------------------------------------
            if self.save_input_data:
                self.input_data_manager.set_trading_date(current_date_str)
                # Set global manager for API calls
                from src.utils.input_data_manager import set_global_input_data_manager
                set_global_input_data_manager(self.input_data_manager)

            # ---------------------------------------------------------------
            # 2) Execute the agent's trades
            # ---------------------------------------------------------------
            output = self.agent(
                tickers=self.tickers,
                start_date=lookback_start,
                end_date=current_date_str,
                portfolio=self.portfolio,
                show_reasoning=self.show_reasoning,
                model_name=self.model_name,
                model_provider=self.model_provider,
                selected_analysts=self.selected_analysts,
            )
            decisions = output["decisions"]
            analyst_signals = output["analyst_signals"]

            # Save reasoning to files if enabled
            if self.save_reasoning:
                # Save analyst signals reasoning
                for agent_name, signals in analyst_signals.items():
                    for ticker in self.tickers:
                        if ticker in signals:
                            self._save_agent_reasoning(
                                signals[ticker],
                                agent_name,
                                ticker,
                                current_date_str
                            )

                # Save portfolio manager reasoning - extract from decisions
                for ticker in self.tickers:
                    if ticker in decisions:
                        # Portfolio manager reasoning is in the decisions structure
                        portfolio_decision = decisions[ticker]
                        # Create a proper reasoning structure for portfolio manager
                        portfolio_reasoning = {
                            "action": portfolio_decision.get("action", "hold"),
                            "quantity": portfolio_decision.get("quantity", 0),
                            "confidence": portfolio_decision.get("confidence", 0.0),
                            "reasoning": portfolio_decision.get("reasoning", "No reasoning provided")
                        }
                        self._save_agent_reasoning(
                            portfolio_reasoning,
                            "portfolio_manager_agent",
                            ticker,
                            current_date_str
                        )

            # Track signal accuracy if enabled
            if self.track_accuracy:
                for ticker in self.tickers:
                    # Track current day signals
                    self.accuracy_tracker.track_daily_signals(analyst_signals, current_date_str, ticker)

                    # Evaluate previous day accuracy (if not first day)
                    if hasattr(self, '_previous_date'):
                        evaluation = self.accuracy_tracker.evaluate_previous_day_accuracy(current_date_str, ticker)
                        if evaluation:
                            # Display cumulative stats every few days
                            if len(self.accuracy_tracker.accuracy_history) > 0 and i % 5 == 0:
                                self.accuracy_tracker.display_cumulative_stats()

                # Store current date for next iteration
                self._previous_date = current_date_str

            # Execute trades for each ticker with validation
            executed_trades = {}
            for ticker in self.tickers:
                decision = decisions.get(ticker, {"action": "hold", "quantity": 0})
                action, quantity = decision.get("action", "hold"), decision.get("quantity", 0)

                # Validate trading decision against current positions
                position = self.portfolio["positions"][ticker]
                current_long = position["long"]
                current_short = position["short"]

                # Check for invalid trading decisions and warn
                if action == "sell" and current_long == 0:
                    print(f"⚠️  WARNING: Portfolio manager attempted to SELL {ticker} but current long position is 0. Changing to HOLD.")
                    action = "hold"
                    quantity = 0
                elif action == "cover" and current_short == 0:
                    print(f"⚠️  WARNING: Portfolio manager attempted to COVER {ticker} but current short position is 0. Changing to HOLD.")
                    action = "hold"
                    quantity = 0
                elif action == "sell" and quantity > current_long:
                    print(f"⚠️  WARNING: Portfolio manager attempted to SELL {quantity} shares of {ticker} but only owns {current_long}. Adjusting to maximum sellable.")
                    quantity = current_long
                elif action == "cover" and quantity > current_short:
                    print(f"⚠️  WARNING: Portfolio manager attempted to COVER {quantity} shares of {ticker} but only short {current_short}. Adjusting to maximum coverable.")
                    quantity = current_short

                executed_quantity = self.execute_trade(ticker, action, quantity, current_prices[ticker])
                executed_trades[ticker] = executed_quantity

            # ---------------------------------------------------------------
            # 2) Now that trades have executed trades, recalculate the final
            #    portfolio value for this day.
            # ---------------------------------------------------------------
            total_value = self.calculate_portfolio_value(current_prices)

            # Also compute long/short exposures for final post‐trade state
            long_exposure = sum(self.portfolio["positions"][t]["long"] * current_prices[t] for t in self.tickers)
            short_exposure = sum(self.portfolio["positions"][t]["short"] * current_prices[t] for t in self.tickers)

            # Calculate gross and net exposures
            gross_exposure = long_exposure + short_exposure
            net_exposure = long_exposure - short_exposure
            long_short_ratio = long_exposure / short_exposure if short_exposure > 1e-9 else float("inf")

            # Track each day's portfolio value in self.portfolio_values
            self.portfolio_values.append({"Date": current_date, "Portfolio Value": total_value, "Long Exposure": long_exposure, "Short Exposure": short_exposure, "Gross Exposure": gross_exposure, "Net Exposure": net_exposure, "Long/Short Ratio": long_short_ratio})

            # ---------------------------------------------------------------
            # 3) Build the table rows to display
            # ---------------------------------------------------------------
            date_rows = []

            # For each ticker, record signals/trades
            for ticker in self.tickers:
                ticker_signals = {}
                for agent_name, signals in analyst_signals.items():
                    if ticker in signals:
                        ticker_signals[agent_name] = signals[ticker]

                bullish_count = len([s for s in ticker_signals.values() if s.get("signal", "").lower() == "bullish"])
                bearish_count = len([s for s in ticker_signals.values() if s.get("signal", "").lower() == "bearish"])
                neutral_count = len([s for s in ticker_signals.values() if s.get("signal", "").lower() == "neutral"])

                # Calculate net position value
                pos = self.portfolio["positions"][ticker]
                long_val = pos["long"] * current_prices[ticker]
                short_val = pos["short"] * current_prices[ticker]
                net_position_value = long_val - short_val

                # Get the action and quantity from the decisions
                action = decisions.get(ticker, {}).get("action", "hold")
                quantity = executed_trades.get(ticker, 0)

                # Append the agent action to the table rows
                date_rows.append(
                    format_backtest_row(
                        date=current_date_str,
                        ticker=ticker,
                        action=action,
                        quantity=quantity,
                        price=current_prices[ticker],
                        shares_owned=pos["long"] - pos["short"],  # net shares
                        position_value=net_position_value,
                        bullish_count=bullish_count,
                        bearish_count=bearish_count,
                        neutral_count=neutral_count,
                    )
                )
            # ---------------------------------------------------------------
            # 4) Calculate performance summary metrics
            # ---------------------------------------------------------------
            # Calculate portfolio return vs. initial capital
            # The realized gains are already reflected in cash balance, so we don't add them separately
            portfolio_return = (total_value / self.initial_capital - 1) * 100

            # Add summary row for this day
            date_rows.append(
                format_backtest_row(
                    date=current_date_str,
                    ticker="",
                    action="",
                    quantity=0,
                    price=0,
                    shares_owned=0,
                    position_value=0,
                    bullish_count=0,
                    bearish_count=0,
                    neutral_count=0,
                    is_summary=True,
                    total_value=total_value,
                    return_pct=portfolio_return,
                    cash_balance=self.portfolio["cash"],
                    total_position_value=total_value - self.portfolio["cash"],
                    sharpe_ratio=performance_metrics["sharpe_ratio"],
                    sortino_ratio=performance_metrics["sortino_ratio"],
                    max_drawdown=performance_metrics["max_drawdown"],
                ),
            )

            table_rows.extend(date_rows)
            print_backtest_results(table_rows)

            # Update performance metrics if we have enough data
            if len(self.portfolio_values) > 3:
                self._update_performance_metrics(performance_metrics)

            # Finalize input data for this trading date
            if self.save_input_data:
                self.input_data_manager.finalize_trading_date()

            # Apply rate limiting delay if using meta-llama/llama-4-scout-17b-16e-instruct
            # This delay occurs between trading days to avoid hitting API rate limits
            self._apply_rate_limit_delay(current_date_str)

            # 在交易日结束时清理SPY缓存，为下一个交易日做准备
            from src.tools.free_market_data import clear_spy_cache
            clear_spy_cache()

        # Store the final performance metrics for reference in analyze_performance
        self.performance_metrics = performance_metrics

        # Save final accuracy report if tracking is enabled
        if self.track_accuracy:
            experiment_name = f"experiment_{datetime.now().strftime('%Y-%m-%d')}"
            for ticker in self.tickers:
                self.accuracy_tracker.save_final_report(experiment_name, ticker)

            # Display final cumulative stats
            print("\n" + "="*60)
            print("📊 最终准确性统计")
            print("="*60)
            self.accuracy_tracker.display_cumulative_stats()

        return performance_metrics

    def _update_performance_metrics(self, performance_metrics):
        """Helper method to update performance metrics using daily returns."""
        values_df = pd.DataFrame(self.portfolio_values).set_index("Date")
        values_df["Daily Return"] = values_df["Portfolio Value"].pct_change()
        clean_returns = values_df["Daily Return"].dropna()

        if len(clean_returns) < 2:
            return  # not enough data points

        # Assumes 252 trading days/year
        daily_risk_free_rate = 0.0434 / 252
        excess_returns = clean_returns - daily_risk_free_rate
        mean_excess_return = excess_returns.mean()
        std_excess_return = excess_returns.std()

        # Sharpe ratio
        if std_excess_return > 1e-12:
            performance_metrics["sharpe_ratio"] = np.sqrt(252) * (mean_excess_return / std_excess_return)
        else:
            performance_metrics["sharpe_ratio"] = 0.0

        # Sortino ratio
        negative_returns = excess_returns[excess_returns < 0]
        if len(negative_returns) > 0:
            downside_std = negative_returns.std()
            if downside_std > 1e-12:
                performance_metrics["sortino_ratio"] = np.sqrt(252) * (mean_excess_return / downside_std)
            else:
                performance_metrics["sortino_ratio"] = float("inf") if mean_excess_return > 0 else 0
        else:
            performance_metrics["sortino_ratio"] = float("inf") if mean_excess_return > 0 else 0

        # Maximum drawdown (ensure it's stored as a negative percentage)
        rolling_max = values_df["Portfolio Value"].cummax()
        drawdown = (values_df["Portfolio Value"] - rolling_max) / rolling_max

        if len(drawdown) > 0:
            min_drawdown = drawdown.min()
            # Store as a negative percentage
            performance_metrics["max_drawdown"] = min_drawdown * 100

            # Store the date of max drawdown for reference
            if min_drawdown < 0:
                performance_metrics["max_drawdown_date"] = drawdown.idxmin().strftime("%Y-%m-%d")
            else:
                performance_metrics["max_drawdown_date"] = None
        else:
            performance_metrics["max_drawdown"] = 0.0
            performance_metrics["max_drawdown_date"] = None

    def analyze_performance(self):
        """Creates a performance DataFrame, prints summary stats, and plots equity curve."""
        if not self.portfolio_values:
            print("No portfolio data found. Please run the backtest first.")
            return pd.DataFrame()

        performance_df = pd.DataFrame(self.portfolio_values).set_index("Date")
        if performance_df.empty:
            print("No valid performance data to analyze.")
            return performance_df

        final_portfolio_value = performance_df["Portfolio Value"].iloc[-1]
        total_return = ((final_portfolio_value - self.initial_capital) / self.initial_capital) * 100

        print(f"\n{Fore.WHITE}{Style.BRIGHT}PORTFOLIO PERFORMANCE SUMMARY:{Style.RESET_ALL}")
        print(f"Total Return: {Fore.GREEN if total_return >= 0 else Fore.RED}{total_return:.2f}%{Style.RESET_ALL}")

        # Print realized P&L for informational purposes only
        total_realized_gains = sum(self.portfolio["realized_gains"][ticker]["long"] + self.portfolio["realized_gains"][ticker]["short"] for ticker in self.tickers)
        print(f"Total Realized Gains/Losses: {Fore.GREEN if total_realized_gains >= 0 else Fore.RED}${total_realized_gains:,.2f}{Style.RESET_ALL}")

        # Plot the portfolio value over time
        plt.figure(figsize=(12, 6))
        plt.plot(performance_df.index, performance_df["Portfolio Value"], color="blue")
        plt.title("Portfolio Value Over Time")
        plt.ylabel("Portfolio Value ($)")
        plt.xlabel("Date")
        plt.grid(True)
        plt.show()

        # Compute daily returns
        performance_df["Daily Return"] = performance_df["Portfolio Value"].pct_change().fillna(0)
        daily_rf = 0.0434 / 252  # daily risk-free rate
        mean_daily_return = performance_df["Daily Return"].mean()
        std_daily_return = performance_df["Daily Return"].std()

        # Annualized Sharpe Ratio
        if std_daily_return != 0:
            annualized_sharpe = np.sqrt(252) * ((mean_daily_return - daily_rf) / std_daily_return)
        else:
            annualized_sharpe = 0
        print(f"\nSharpe Ratio: {Fore.YELLOW}{annualized_sharpe:.2f}{Style.RESET_ALL}")

        # Use the max drawdown value calculated during the backtest if available
        max_drawdown = getattr(self, "performance_metrics", {}).get("max_drawdown")
        max_drawdown_date = getattr(self, "performance_metrics", {}).get("max_drawdown_date")

        # If no value exists yet, calculate it
        if max_drawdown is None:
            rolling_max = performance_df["Portfolio Value"].cummax()
            drawdown = (performance_df["Portfolio Value"] - rolling_max) / rolling_max
            max_drawdown = drawdown.min() * 100
            max_drawdown_date = drawdown.idxmin().strftime("%Y-%m-%d") if pd.notnull(drawdown.idxmin()) else None

        if max_drawdown_date:
            print(f"Maximum Drawdown: {Fore.RED}{abs(max_drawdown):.2f}%{Style.RESET_ALL} (on {max_drawdown_date})")
        else:
            print(f"Maximum Drawdown: {Fore.RED}{abs(max_drawdown):.2f}%{Style.RESET_ALL}")

        # Win Rate
        winning_days = len(performance_df[performance_df["Daily Return"] > 0])
        total_days = max(len(performance_df) - 1, 1)
        win_rate = (winning_days / total_days) * 100
        print(f"Win Rate: {Fore.GREEN}{win_rate:.2f}%{Style.RESET_ALL}")

        # Average Win/Loss Ratio
        positive_returns = performance_df[performance_df["Daily Return"] > 0]["Daily Return"]
        negative_returns = performance_df[performance_df["Daily Return"] < 0]["Daily Return"]
        avg_win = positive_returns.mean() if not positive_returns.empty else 0
        avg_loss = abs(negative_returns.mean()) if not negative_returns.empty else 0
        if avg_loss != 0:
            win_loss_ratio = avg_win / avg_loss
        else:
            win_loss_ratio = float("inf") if avg_win > 0 else 0
        print(f"Win/Loss Ratio: {Fore.GREEN}{win_loss_ratio:.2f}{Style.RESET_ALL}")

        # Maximum Consecutive Wins / Losses
        returns_binary = (performance_df["Daily Return"] > 0).astype(int)
        if len(returns_binary) > 0:
            max_consecutive_wins = max((len(list(g)) for k, g in itertools.groupby(returns_binary) if k == 1), default=0)
            max_consecutive_losses = max((len(list(g)) for k, g in itertools.groupby(returns_binary) if k == 0), default=0)
        else:
            max_consecutive_wins = 0
            max_consecutive_losses = 0

        print(f"Max Consecutive Wins: {Fore.GREEN}{max_consecutive_wins}{Style.RESET_ALL}")
        print(f"Max Consecutive Losses: {Fore.RED}{max_consecutive_losses}{Style.RESET_ALL}")

        return performance_df


### 4. Run the Backtest #####
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run backtesting simulation")
    parser.add_argument(
        "--tickers",
        type=str,
        required=False,
        help="Comma-separated list of stock ticker symbols (e.g., AAPL,MSFT,GOOGL)",
    )
    parser.add_argument(
        "--end-date",
        type=str,
        default=datetime.now().strftime("%Y-%m-%d"),
        help="End date in YYYY-MM-DD format",
    )
    parser.add_argument(
        "--start-date",
        type=str,
        default=(datetime.now() - relativedelta(months=1)).strftime("%Y-%m-%d"),
        help="Start date in YYYY-MM-DD format",
    )
    parser.add_argument(
        "--initial-capital",
        type=float,
        default=100000,
        help="Initial capital amount (default: 100000)",
    )
    parser.add_argument(
        "--margin-requirement",
        type=float,
        default=0.0,
        help="Margin ratio for short positions, e.g. 0.5 for 50%% (default: 0.0)",
    )
    parser.add_argument("--ollama", action="store_true", help="Use Ollama for local LLM inference")
    parser.add_argument("--show-reasoning", action="store_true", help="Display agent reasoning in terminal")
    parser.add_argument("--save-reasoning", action="store_true", help="Save agent reasoning to files")
    parser.add_argument("--save-input-data", action="store_true", help="Save agent input data to files")
    parser.add_argument("--track-accuracy", action="store_true", help="Track signal accuracy in real-time during backtesting")
    parser.add_argument("--deterministic", action="store_true", help="Enable deterministic mode for reproducible results")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for deterministic mode (default: 42)")
    parser.add_argument("--experiment-id", type=str, help="Unique experiment identifier for deterministic mode")

    # Add news configuration arguments
    try:
        from src.config.news_config import add_news_config_args
        add_news_config_args(parser)
    except ImportError:
        print("Warning: News configuration not available")

    args = parser.parse_args()

    # Setup news configuration from command line arguments
    try:
        from src.config.news_config import setup_news_config_from_args
        setup_news_config_from_args(args)
    except ImportError:
        pass

    # Enable deterministic mode if requested
    if args.deterministic and DETERMINISTIC_AVAILABLE:
        experiment_id = args.experiment_id or f"exp_{datetime.now().strftime('%m%d_%H%M')}"
        set_deterministic_mode(True, args.seed, experiment_id)
        print(f"{Fore.GREEN}Deterministic mode enabled{Style.RESET_ALL}")
        print(f"Seed: {args.seed}")
        print(f"Experiment ID: {experiment_id}")
        log_deterministic_state()
    elif args.deterministic and not DETERMINISTIC_AVAILABLE:
        print(f"{Fore.YELLOW}Warning: Deterministic mode requested but not available{Style.RESET_ALL}")

    # Parse tickers from comma-separated string
    tickers = [ticker.strip() for ticker in args.tickers.split(",")] if args.tickers else []

    # Choose analysts
    selected_analysts = None
    choices = questionary.checkbox(
        "Use the Space bar to select/unselect analysts.",
        choices=[questionary.Choice(display, value=value) for display, value in ANALYST_ORDER],
        instruction="\n\nPress 'a' to toggle all.\n\nPress Enter when done to run the hedge fund.",
        validate=lambda x: len(x) > 0 or "You must select at least one analyst.",
        style=questionary.Style(
            [
                ("checkbox-selected", "fg:green"),
                ("selected", "fg:green noinherit"),
                ("highlighted", "noinherit"),
                ("pointer", "noinherit"),
            ]
        ),
    ).ask()

    if not choices:
        print("\n\nInterrupt received. Exiting...")
        sys.exit(0)
    else:
        selected_analysts = choices
        print(f"\nSelected analysts: " f"{', '.join(Fore.GREEN + choice.title().replace('_', ' ') + Style.RESET_ALL for choice in choices)}")

    # Select LLM model based on whether Ollama is being used
    model_name = ""
    model_provider = None

    if args.ollama:
        print(f"{Fore.CYAN}Using Ollama for local LLM inference.{Style.RESET_ALL}")

        # Select from Ollama-specific models
        model_name = questionary.select(
            "Select your Ollama model:",
            choices=[questionary.Choice(display, value=value) for display, value, _ in OLLAMA_LLM_ORDER],
            style=questionary.Style(
                [
                    ("selected", "fg:green bold"),
                    ("pointer", "fg:green bold"),
                    ("highlighted", "fg:green"),
                    ("answer", "fg:green bold"),
                ]
            ),
        ).ask()

        if not model_name:
            print("\n\nInterrupt received. Exiting...")
            sys.exit(0)

        if model_name == "-":
            model_name = questionary.text("Enter the custom model name:").ask()
            if not model_name:
                print("\n\nInterrupt received. Exiting...")
                sys.exit(0)

        # Ensure Ollama is installed, running, and the model is available
        if not ensure_ollama_and_model(model_name):
            print(f"{Fore.RED}Cannot proceed without Ollama and the selected model.{Style.RESET_ALL}")
            sys.exit(1)

        model_provider = ModelProvider.OLLAMA.value
        print(f"\nSelected {Fore.CYAN}Ollama{Style.RESET_ALL} model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")
    else:
        # Use the standard cloud-based LLM selection
        model_choice = questionary.select(
            "Select your LLM model:",
            choices=[questionary.Choice(display, value=(name, provider)) for display, name, provider in LLM_ORDER],
            style=questionary.Style(
                [
                    ("selected", "fg:green bold"),
                    ("pointer", "fg:green bold"),
                    ("highlighted", "fg:green"),
                    ("answer", "fg:green bold"),
                ]
            ),
        ).ask()

        if not model_choice:
            print("\n\nInterrupt received. Exiting...")
            sys.exit(0)

        model_name, model_provider = model_choice

        model_info = get_model_info(model_name, model_provider)
        if model_info:
            if model_info.is_custom():
                model_name = questionary.text("Enter the custom model name:").ask()
                if not model_name:
                    print("\n\nInterrupt received. Exiting...")
                    sys.exit(0)

            print(f"\nSelected {Fore.CYAN}{model_provider}{Style.RESET_ALL} model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")
        else:
            model_provider = "Unknown"
            print(f"\nSelected model: {Fore.GREEN + Style.BRIGHT}{model_name}{Style.RESET_ALL}\n")

    # Create and run the backtester
    backtester = Backtester(
        agent=run_hedge_fund,
        tickers=tickers,
        start_date=args.start_date,
        end_date=args.end_date,
        initial_capital=args.initial_capital,
        model_name=model_name,
        model_provider=model_provider,
        selected_analysts=selected_analysts,
        initial_margin_requirement=args.margin_requirement,
        show_reasoning=args.show_reasoning,
        save_reasoning=args.save_reasoning,
        save_input_data=args.save_input_data,
        track_accuracy=args.track_accuracy,
    )

    performance_metrics = backtester.run_backtest()
    performance_df = backtester.analyze_performance()
